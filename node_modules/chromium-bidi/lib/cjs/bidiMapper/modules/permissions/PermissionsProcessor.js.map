{"version": 3, "file": "PermissionsProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/permissions/PermissionsProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAGH,+DAIuC;AAEvC,MAAa,oBAAoB;IAC/B,iBAAiB,CAAY;IAE7B,YAAY,gBAA2B;QACrC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,MAA2C;QAE3C,IAAI,CAAC;YACH,MAAM,aAAa,GAChB,MAAwC,CAAC,kBAAkB,CAAC;gBAC7D,MAAM,CAAC,WAAW,CAAC;YACrB,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,uBAAuB,EAAE;gBAChE,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,gBAAgB,EACd,aAAa,IAAI,aAAa,KAAK,SAAS;oBAC1C,CAAC,CAAC,aAAa;oBACf,CAAC,CAAC,SAAS;gBACf,UAAU,EAAE;oBACV,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI;iBAC7B;gBACD,OAAO,EAAE,MAAM,CAAC,KAAK;aACtB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IACG,GAAa,CAAC,OAAO;gBACtB,gDAAgD,EAChD,CAAC;gBACD,gEAAgE;gBAChE,qBAAqB;gBACrB,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,MAAM,IAAI,sCAAwB,CAAE,GAAa,CAAC,OAAO,CAAC,CAAC;QAC7D,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;CACF;AAtCD,oDAsCC"}