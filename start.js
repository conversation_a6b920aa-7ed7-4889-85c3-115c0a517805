const { spawn } = require('child_process');
const colors = require('colors');
const fs = require('fs');

console.log(colors.cyan('=' * 60));
console.log(colors.cyan('🚀 BBVERSE BOT STARTUP SCRIPT'));
console.log(colors.cyan('=' * 60));

async function runCommand(command, args = []) {
  return new Promise((resolve, reject) => {
    console.log(colors.yellow(`\n▶️ Running: ${command} ${args.join(' ')}`));
    
    const process = spawn(command, args, {
      stdio: 'inherit',
      shell: true
    });

    process.on('close', (code) => {
      if (code === 0) {
        console.log(colors.green(`✅ ${command} completed successfully`));
        resolve();
      } else {
        console.log(colors.red(`❌ ${command} failed with code ${code}`));
        reject(new Error(`Process failed with code ${code}`));
      }
    });

    process.on('error', (error) => {
      console.log(colors.red(`❌ Error running ${command}: ${error.message}`));
      reject(error);
    });
  });
}

async function checkProxies() {
  try {
    if (fs.existsSync('proxy.txt')) {
      const content = fs.readFileSync('proxy.txt', 'utf-8').trim();
      const proxies = content.split('\n').filter(Boolean);
      
      if (proxies.length >= 5) {
        console.log(colors.green(`✅ Found ${proxies.length} existing proxies in proxy.txt`));
        return true;
      }
    }
    return false;
  } catch (error) {
    return false;
  }
}

async function main() {
  try {
    console.log(colors.blue('🔍 Checking proxy status...'));
    
    const hasProxies = await checkProxies();
    
    if (!hasProxies) {
      console.log(colors.yellow('📡 No sufficient proxies found, starting proxy scraper...'));
      await runCommand('node', ['proxy-scraper.js']);
    } else {
      console.log(colors.green('✅ Sufficient proxies available, skipping scraper'));
    }
    
    console.log(colors.blue('\n🤖 Starting BBverse bot...'));
    await runCommand('node', ['allinone.js']);
    
  } catch (error) {
    console.log(colors.red(`\n❌ Startup failed: ${error.message}`));
    process.exit(1);
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log(colors.yellow('\n\n⚠️ Startup interrupted by user'));
  process.exit(0);
});

main();
