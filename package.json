{"scripts": {"start": "node start.js", "create-wallet": "node proxy-scraper.js && node main.js", "scrape-proxy": "node proxy-scraper.js", "bot": "node allinone.js"}, "dependencies": {"@solana/web3.js": "^1.98.2", "axios": "^1.11.0", "bip39": "^3.1.0", "bs58": "^5.0.0", "cli-progress": "^3.12.0", "colors": "^1.4.0", "dotenv": "^17.2.1", "ed25519-hd-key": "^1.3.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "puppeteer": "^24.15.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-page-proxy": "^1.3.0", "readline-sync": "^1.4.10", "tweetnacl": "^1.0.3", "uuid": "^11.1.0", "winston": "^3.17.0"}}