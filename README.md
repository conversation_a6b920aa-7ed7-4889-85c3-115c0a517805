 <h1 align="center">ADB NODE</h1>
<h3 align="center">Join Here - https://bubuverse.fun?ref=ArQbfk3vCPLBS9AApgdYNmSf6hWwe8tYbciErJCRyTn5 - Retroactive with me</h3>


# Installation Guide for BubuVerse Register & Ref Bot

**Functionality**: Automatically creates Solana wallets and uses <PERSON><PERSON><PERSON><PERSON> to bypass Vercel's checkpoint, while completing the registration process based on the specified referral address.  
Automates the opening of Boxes through `OpenBox` and stakes the received NFTs through `StakeNFT`.  
**Update added**: Daily Check-in and Energy Collection once per day through `DailyCollect`.

---

## Instalation

1. **Clone The Repositories:**
   ```bash
   git clone https://github.com/airdropbomb/BBverse.git && cd BBverse
   ```
---

2. **Install Requirements:**
   ```bash
   npm install
   ```

3. **proxy.txt**: (Must Need)

   ```bash
   nano proxy.txt
   ```
     ```
     ***************************:port
     ***************************:port
     ```
4. **wallet Create**
   ```bash
   node main.js
   ```
   (After wallet create store in the `wallet_sol.json`)

5. **Run Daily/Openbox/Nft Stake**
   ```bash
   node allinone.js
   ```

