 <h1 align="center">B<PERSON><PERSON><PERSON><PERSON> BOT WITH AUTO PROXY SCRAPER</h1>
<h3 align="center">Join Here - https://bubuverse.fun?ref=ArQbfk3vCPLBS9AApgdYNmSf6hWwe8tYbciErJCRyTn5 - Retroactive with me</h3>

# 🚀 BubuVerse Automation Bot with Smart Proxy System

**New Features:**
- ✅ **Auto Proxy Scraper**: Automatically scrapes and validates working proxies
- ✅ **Smart Proxy Rotation**: Auto-switches to next proxy when one fails
- ✅ **Error Recovery**: Continues operation even if some proxies fail
- ✅ **One Command Start**: `npm start` does everything automatically

**Functionality**:
- Automatically creates Solana wallets and uses Puppeteer to bypass Vercel's checkpoint
- Completes registration process with referral address
- Automates Box opening and NFT staking
- Daily Check-in and Energy Collection
- **NEW**: Auto proxy management with rotation system

---

## 📦 Installation

1. **Clone The Repository:**
   ```bash
   git clone https://github.com/airdropbomb/BBverse.git && cd BBverse
   ```

2. **Install Dependencies:**
   ```bash
   npm install
   ```

---

## 🎯 Usage (New Simplified Method)

### **Option 1: Auto Start (Recommended)**
```bash
npm start
```
This will:
1. 🔍 Auto-scrape 20 working proxies
2. 🤖 Start the bot automatically
3. 🔄 Use proxy rotation for reliability

### **Option 2: Create Wallets First**
```bash
npm run create-wallet
```
This will:
1. 🔍 Auto-scrape proxies
2. 💰 Create new wallets
3. 💾 Save to `wallet_sol.json`

### **Option 3: Manual Proxy Scraping**
```bash
npm run scrape-proxy
```
Only scrapes proxies without running the bot

### **Option 4: Run Bot Only**
```bash
npm run bot
```
Runs bot with existing proxies (no scraping)

---

## 📋 Available Scripts

| Command | Description |
|---------|-------------|
| `npm start` | 🚀 Auto-scrape proxies + Run bot |
| `npm run create-wallet` | 💰 Auto-scrape proxies + Create wallets |
| `npm run scrape-proxy` | 🔍 Scrape proxies only |
| `npm run bot` | 🤖 Run bot only (requires existing proxies) |

---

## 🔧 How It Works

1. **Proxy Scraper** (`proxy-scraper.js`):
   - Scrapes from multiple sources (GitHub, ProxyScrape, etc.)
   - Validates each proxy automatically
   - Saves 20 working proxies to `proxy.txt`
   - Smart caching (won't re-scrape if enough proxies exist)

2. **Proxy Rotation System**:
   - Automatically rotates to next proxy on failure
   - Marks failed proxies and avoids them
   - Continues operation with remaining working proxies
   - Resets failed list when all proxies exhausted

3. **Bot Features**:
   - Daily check-in automation
   - Box opening with NFT collection
   - NFT staking for rewards
   - Energy collection system

---

## 📁 Generated Files

- `proxy.txt` - Working proxies (auto-generated)
- `wallet_sol.json` - Created wallets with private keys
- `open.json` - NFT collection data
- `ua.txt` - User agents for browser simulation

---

## ⚠️ Important Notes

- **No manual proxy setup needed** - everything is automated
- Proxies are validated before use for reliability
- Bot will continue working even if some proxies fail
- All wallet data is stored locally and securely
- Run `npm start` and let it handle everything automatically

---

## 🛡️ Security

This bot is **100% safe** - no wallet draining or data theft:
- ✅ All communications only to `bubuverse.fun`
- ✅ Private keys never sent to external servers
- ✅ No hardcoded developer wallets
- ✅ Open source and transparent code

