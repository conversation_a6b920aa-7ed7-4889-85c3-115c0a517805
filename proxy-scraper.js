const axios = require('axios');
const fs = require('fs');
const colors = require('colors');

class ProxyScraper {
  constructor() {
    this.headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    };
    this.timeout = 8000;
    this.maxProxies = 20;
    this.workingProxies = [];
  }

  printBanner() {
    console.log(colors.cyan('=' * 60));
    console.log(colors.cyan('🔍 PROXY SCRAPER FOR BBVERSE BOT'));
    console.log(colors.cyan('=' * 60));
    console.log(colors.yellow('📡 Scraping proxies from multiple sources...'));
    console.log(colors.yellow('✅ Auto-validation enabled'));
    console.log(colors.yellow(`🎯 Target: ${this.maxProxies} working proxies`));
    console.log(colors.cyan('=' * 60));
  }

  async scrapeProxyScrape() {
    console.log(colors.blue('📡 Scraping ProxyScrape...'));
    
    const urls = [
      'https://raw.githubusercontent.com/monosans/proxy-list/refs/heads/main/proxies/http.txt',
      'https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&format=textplain',
      'https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt'
    ];

    const proxies = [];

    for (const url of urls) {
      try {
        const response = await axios.get(url, {
          headers: this.headers,
          timeout: 15000
        });

        if (response.status === 200) {
          const lines = response.data.trim().split('\n');
          for (const line of lines) {
            const proxy = line.trim();
            if (this.isValidProxy(proxy)) {
              proxies.push(proxy);
            }
          }
        }
      } catch (error) {
        console.log(colors.red(`   ⚠️ Error: ${error.message.substring(0, 50)}...`));
      }
    }

    console.log(colors.green(`   ✅ Found ${proxies.length} proxies`));
    return proxies;
  }

  async scrapeFreeProxyList() {
    console.log(colors.blue('📡 Scraping Free-Proxy-List...'));
    
    const urls = [
      'https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt',
      'https://raw.githubusercontent.com/ShiftyTR/Proxy-List/master/http.txt',
      'https://raw.githubusercontent.com/roosterkid/openproxylist/main/HTTPS_RAW.txt'
    ];

    const proxies = [];

    for (const url of urls) {
      try {
        const response = await axios.get(url, {
          headers: this.headers,
          timeout: 15000
        });

        if (response.status === 200) {
          const lines = response.data.trim().split('\n');
          for (const line of lines) {
            const proxy = line.trim();
            if (this.isValidProxy(proxy)) {
              proxies.push(proxy);
            }
          }
        }
      } catch (error) {
        console.log(colors.red(`   ⚠️ Error: ${error.message.substring(0, 50)}...`));
      }
    }

    console.log(colors.green(`   ✅ Found ${proxies.length} proxies`));
    return proxies;
  }

  isValidProxy(proxy) {
    if (!proxy || !proxy.includes(':')) {
      return false;
    }

    try {
      // Remove protocol if present
      if (proxy.includes('://')) {
        proxy = proxy.split('://', 2)[1];
      }

      const parts = proxy.split(':');
      if (parts.length !== 2) {
        return false;
      }

      const [ip, port] = parts;

      // Validate IP
      const ipParts = ip.split('.');
      if (ipParts.length !== 4) {
        return false;
      }

      for (const part of ipParts) {
        const num = parseInt(part);
        if (isNaN(num) || num < 0 || num > 255) {
          return false;
        }
      }

      // Validate port
      const portNum = parseInt(port);
      if (isNaN(portNum) || portNum < 1 || portNum > 65535) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  async validateProxy(proxy) {
    const testUrls = [
      'http://httpbin.org/ip',
      'https://api.ipify.org?format=json',
      'http://icanhazip.com'
    ];

    const startTime = Date.now();

    try {
      const proxyUrl = proxy.includes('://') ? proxy : `http://${proxy}`;
      
      for (const testUrl of testUrls) {
        try {
          const response = await axios.get(testUrl, {
            proxy: false,
            httpsAgent: new (require('https-proxy-agent'))(proxyUrl),
            httpAgent: new (require('http-proxy-agent'))(proxyUrl),
            timeout: this.timeout,
            headers: this.headers
          });

          if (response.status === 200) {
            const responseTime = Date.now() - startTime;
            return { working: true, responseTime };
          }
        } catch {
          continue;
        }
      }

      return { working: false, responseTime: 0 };
    } catch {
      return { working: false, responseTime: 0 };
    }
  }

  async validateProxies(proxies) {
    if (!proxies || proxies.length === 0) {
      return [];
    }

    console.log(colors.yellow(`🔍 Validating ${Math.min(proxies.length, 50)} proxies...`));
    
    const working = [];
    const sampleProxies = proxies.slice(0, 50); // Test only first 50 for speed

    for (let i = 0; i < sampleProxies.length && working.length < this.maxProxies; i++) {
      const proxy = sampleProxies[i];
      
      process.stdout.write(colors.gray(`   Testing ${i + 1}/${sampleProxies.length}: ${proxy.substring(0, 20)}... `));
      
      const result = await this.validateProxy(proxy);
      
      if (result.working) {
        working.push(proxy);
        console.log(colors.green(`✅ (${result.responseTime}ms)`));
        
        if (working.length >= this.maxProxies) {
          console.log(colors.green(`🎯 Reached target of ${this.maxProxies} working proxies!`));
          break;
        }
      } else {
        console.log(colors.red('❌'));
      }
    }

    return working;
  }

  async scrapeAllSources() {
    console.log(colors.yellow('🚀 Starting proxy scraping...'));
    
    const allProxies = [];

    try {
      // Scrape from ProxyScrape
      const proxyScrapeProxies = await this.scrapeProxyScrape();
      allProxies.push(...proxyScrapeProxies);

      // Scrape from Free Proxy List
      const freeProxyProxies = await this.scrapeFreeProxyList();
      allProxies.push(...freeProxyProxies);

    } catch (error) {
      console.log(colors.red(`❌ Error during scraping: ${error.message}`));
    }

    // Remove duplicates
    const uniqueProxies = [...new Set(allProxies)];
    console.log(colors.blue(`\n📊 Total unique proxies found: ${uniqueProxies.length}`));

    return uniqueProxies;
  }

  saveToFile(proxies, filename = 'proxy.txt') {
    try {
      const content = proxies.join('\n') + '\n';
      fs.writeFileSync(filename, content);
      console.log(colors.green(`💾 Saved ${proxies.length} working proxies to ${filename}`));
    } catch (error) {
      console.log(colors.red(`❌ Error saving to file: ${error.message}`));
    }
  }

  async run() {
    this.printBanner();

    try {
      // Check if proxy.txt exists and has content
      if (fs.existsSync('proxy.txt')) {
        const existingContent = fs.readFileSync('proxy.txt', 'utf-8').trim();
        const existingProxies = existingContent.split('\n').filter(Boolean);
        
        if (existingProxies.length >= 5) {
          console.log(colors.green(`✅ Found ${existingProxies.length} existing proxies in proxy.txt`));
          console.log(colors.yellow('🔄 Skipping scraping (sufficient proxies available)'));
          console.log(colors.cyan('=' * 60));
          return;
        }
      }

      // Scrape from all sources
      const allProxies = await this.scrapeAllSources();

      if (allProxies.length === 0) {
        console.log(colors.red('❌ No proxies found from any source'));
        return;
      }

      // Validate proxies
      const workingProxies = await this.validateProxies(allProxies);

      if (workingProxies.length > 0) {
        // Save to file
        this.saveToFile(workingProxies);

        console.log(colors.green('\n✅ SUCCESS!'));
        console.log(colors.blue(`📊 Found ${workingProxies.length} working proxies`));
        console.log(colors.blue(`📈 Success rate: ${((workingProxies.length / Math.min(allProxies.length, 50)) * 100).toFixed(1)}%`));

        // Show sample
        console.log(colors.yellow('\n📋 Sample proxies:'));
        workingProxies.slice(0, 5).forEach((proxy, i) => {
          console.log(colors.white(`   ${i + 1}. ${proxy}`));
        });
        if (workingProxies.length > 5) {
          console.log(colors.gray(`   ... and ${workingProxies.length - 5} more`));
        }
      } else {
        console.log(colors.red('❌ No working proxies found'));
        // Create empty proxy.txt to prevent infinite scraping
        this.saveToFile([]);
      }

    } catch (error) {
      console.log(colors.red(`❌ Error: ${error.message}`));
    }

    console.log(colors.cyan('=' * 60));
  }
}

// Main execution
async function main() {
  const scraper = new ProxyScraper();
  await scraper.run();
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = ProxyScraper;
